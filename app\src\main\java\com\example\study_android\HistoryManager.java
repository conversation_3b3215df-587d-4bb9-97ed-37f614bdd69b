package com.example.study_android;

import android.content.Context;
import android.content.SharedPreferences;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import java.util.ArrayList;
import java.util.List;

public class HistoryManager {
    private static final String PREF_NAME = "browser_history";
    private static final String KEY_HISTORY = "history_data";
    private SharedPreferences sharedPreferences;
    private List<HistoryItem> historyList;

    public HistoryManager(Context context) {
        sharedPreferences = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        historyList = new ArrayList<>();
        loadHistory();
    }

    public void addHistory(String title, String url) {
        // 检查是否已存在相同URL，如果存在则更新时间戳
        for (int i = 0; i < historyList.size(); i++) {
            if (historyList.get(i).getUrl().equals(url)) {
                historyList.get(i).setTimestamp(System.currentTimeMillis());
                historyList.get(i).setTitle(title);
                saveHistory();
                return;
            }
        }
        
        // 添加新的历史记录到列表开头
        historyList.add(0, new HistoryItem(title, url));
        
        // 限制历史记录数量（最多保存1000条）
        if (historyList.size() > 1000) {
            historyList.remove(historyList.size() - 1);
        }
        
        saveHistory();
    }

    public List<HistoryItem> getHistory() {
        return new ArrayList<>(historyList);
    }

    public void deleteHistory(HistoryItem item) {
        historyList.remove(item);
        saveHistory();
    }

    public void deleteSelectedHistory() {
        List<HistoryItem> toRemove = new ArrayList<>();
        for (HistoryItem item : historyList) {
            if (item.isSelected()) {
                toRemove.add(item);
            }
        }
        historyList.removeAll(toRemove);
        saveHistory();
    }

    public void clearAllHistory() {
        historyList.clear();
        saveHistory();
    }

    public void selectAll(boolean select) {
        for (HistoryItem item : historyList) {
            item.setSelected(select);
        }
    }

    public String exportToJson() {
        JSONArray jsonArray = new JSONArray();
        try {
            for (HistoryItem item : historyList) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("title", item.getTitle());
                jsonObject.put("url", item.getUrl());
                jsonObject.put("timestamp", item.getTimestamp());
                jsonArray.put(jsonObject);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return jsonArray.toString();
    }

    public boolean importFromJson(String jsonString) {
        try {
            JSONArray jsonArray = new JSONArray(jsonString);
            List<HistoryItem> importedList = new ArrayList<>();
            
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String title = jsonObject.getString("title");
                String url = jsonObject.getString("url");
                long timestamp = jsonObject.getLong("timestamp");
                importedList.add(new HistoryItem(title, url, timestamp));
            }
            
            // 合并导入的历史记录，避免重复
            for (HistoryItem importedItem : importedList) {
                boolean exists = false;
                for (HistoryItem existingItem : historyList) {
                    if (existingItem.getUrl().equals(importedItem.getUrl())) {
                        exists = true;
                        break;
                    }
                }
                if (!exists) {
                    historyList.add(importedItem);
                }
            }
            
            // 按时间戳排序（最新的在前面）
            historyList.sort((a, b) -> Long.compare(b.getTimestamp(), a.getTimestamp()));
            
            saveHistory();
            return true;
        } catch (JSONException e) {
            e.printStackTrace();
            return false;
        }
    }

    private void saveHistory() {
        JSONArray jsonArray = new JSONArray();
        try {
            for (HistoryItem item : historyList) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("title", item.getTitle());
                jsonObject.put("url", item.getUrl());
                jsonObject.put("timestamp", item.getTimestamp());
                jsonArray.put(jsonObject);
            }
            
            SharedPreferences.Editor editor = sharedPreferences.edit();
            editor.putString(KEY_HISTORY, jsonArray.toString());
            editor.apply();
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void loadHistory() {
        String jsonString = sharedPreferences.getString(KEY_HISTORY, "");
        if (!jsonString.isEmpty()) {
            try {
                JSONArray jsonArray = new JSONArray(jsonString);
                historyList.clear();
                
                for (int i = 0; i < jsonArray.length(); i++) {
                    JSONObject jsonObject = jsonArray.getJSONObject(i);
                    String title = jsonObject.getString("title");
                    String url = jsonObject.getString("url");
                    long timestamp = jsonObject.getLong("timestamp");
                    historyList.add(new HistoryItem(title, url, timestamp));
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
    }
}