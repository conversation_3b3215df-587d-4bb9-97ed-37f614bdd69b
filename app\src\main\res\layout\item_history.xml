<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="12dp"
    android:gravity="center_vertical"
    android:background="?android:attr/selectableItemBackground">

    <CheckBox
        android:id="@+id/checkBox"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="12dp" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="网页标题"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="#333333"
            android:maxLines="1"
            android:ellipsize="end" />

        <TextView
            android:id="@+id/tvUrl"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="https://www.example.com"
            android:textSize="12sp"
            android:textColor="#666666"
            android:maxLines="1"
            android:ellipsize="end"
            android:layout_marginTop="2dp" />

        <TextView
            android:id="@+id/tvTime"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="2024-01-01 12:00:00"
            android:textSize="10sp"
            android:textColor="#999999"
            android:layout_marginTop="2dp" />

    </LinearLayout>

    <Button
        android:id="@+id/btnDelete"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:text="删除"
        android:textSize="10sp"
        android:layout_marginLeft="8dp" />

</LinearLayout>