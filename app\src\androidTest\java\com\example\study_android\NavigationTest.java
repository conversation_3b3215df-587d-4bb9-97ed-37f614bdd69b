package com.example.study_android;

import androidx.test.ext.junit.rules.ActivityScenarioRule;
import androidx.test.ext.junit.runners.AndroidJUnit4;
import androidx.test.espresso.Espresso;
import androidx.test.espresso.action.ViewActions;
import androidx.test.espresso.assertion.ViewAssertions;
import androidx.test.espresso.matcher.ViewMatchers;

import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;

@RunWith(AndroidJUnit4.class)
public class NavigationTest {

    @Rule
    public ActivityScenarioRule<MainActivity> activityRule =
            new ActivityScenarioRule<>(MainActivity.class);

    @Test
    public void testNavigationFlow() {
        // 验证在页面A（首页）
        Espresso.onView(ViewMatchers.withId(R.id.titleText))
                .check(ViewAssertions.matches(ViewMatchers.withText("页面A - 首页")));

        // 点击跳转到页面B
        Espresso.onView(ViewMatchers.withId(R.id.goToNextButton))
                .perform(ViewActions.click());

        // 验证在页面B
        Espresso.onView(ViewMatchers.withId(R.id.titleText))
                .check(ViewAssertions.matches(ViewMatchers.withText("页面B")));

        // 验证返回按钮可见
        Espresso.onView(ViewMatchers.withId(R.id.goBackButton))
                .check(ViewAssertions.matches(ViewMatchers.isDisplayed()));

        Espresso.onView(ViewMatchers.withId(R.id.goToHomeButton))
                .check(ViewAssertions.matches(ViewMatchers.isDisplayed()));

        // 点击跳转到页面C
        Espresso.onView(ViewMatchers.withId(R.id.goToNextButton))
                .perform(ViewActions.click());

        // 验证在页面C
        Espresso.onView(ViewMatchers.withId(R.id.titleText))
                .check(ViewAssertions.matches(ViewMatchers.withText("页面C")));

        // 验证下一页按钮不可见（因为C是最后一页）
        Espresso.onView(ViewMatchers.withId(R.id.goToNextButton))
                .check(ViewAssertions.matches(ViewMatchers.withEffectiveVisibility(ViewMatchers.Visibility.GONE)));
    }

    @Test
    public void testBackNavigation() {
        // 导航到页面B
        Espresso.onView(ViewMatchers.withId(R.id.goToNextButton))
                .perform(ViewActions.click());

        // 导航到页面C
        Espresso.onView(ViewMatchers.withId(R.id.goToNextButton))
                .perform(ViewActions.click());

        // 点击返回上一页（从C回到B）
        Espresso.onView(ViewMatchers.withId(R.id.goBackButton))
                .perform(ViewActions.click());

        // 验证回到页面B
        Espresso.onView(ViewMatchers.withId(R.id.titleText))
                .check(ViewAssertions.matches(ViewMatchers.withText("页面B")));
    }

    @Test
    public void testHomeNavigation() {
        // 导航到页面B
        Espresso.onView(ViewMatchers.withId(R.id.goToNextButton))
                .perform(ViewActions.click());

        // 导航到页面C
        Espresso.onView(ViewMatchers.withId(R.id.goToNextButton))
                .perform(ViewActions.click());

        // 点击返回首页
        Espresso.onView(ViewMatchers.withId(R.id.goToHomeButton))
                .perform(ViewActions.click());

        // 验证回到首页
        Espresso.onView(ViewMatchers.withId(R.id.titleText))
                .check(ViewAssertions.matches(ViewMatchers.withText("页面A - 首页")));
    }
}
