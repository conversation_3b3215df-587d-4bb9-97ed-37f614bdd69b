package com.example.study_android;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.TextView;
import java.util.List;

public class HistoryAdapter extends BaseAdapter {
    private Context context;
    private List<HistoryItem> historyList;
    private LayoutInflater inflater;
    private OnHistoryItemClickListener listener;

    public interface OnHistoryItemClickListener {
        void onItemClick(HistoryItem item);
        void onDeleteClick(HistoryItem item);
        void onCheckChanged(HistoryItem item, boolean isChecked);
    }

    public HistoryAdapter(Context context, List<HistoryItem> historyList) {
        this.context = context;
        this.historyList = historyList;
        this.inflater = LayoutInflater.from(context);
    }

    public void setOnHistoryItemClickListener(OnHistoryItemClickListener listener) {
        this.listener = listener;
    }

    @Override
    public int getCount() {
        return historyList.size();
    }

    @Override
    public Object getItem(int position) {
        return historyList.get(position);
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        ViewHolder holder;
        
        if (convertView == null) {
            convertView = inflater.inflate(R.layout.item_history, parent, false);
            holder = new ViewHolder();
            holder.checkBox = convertView.findViewById(R.id.checkBox);
            holder.tvTitle = convertView.findViewById(R.id.tvTitle);
            holder.tvUrl = convertView.findViewById(R.id.tvUrl);
            holder.tvTime = convertView.findViewById(R.id.tvTime);
            holder.btnDelete = convertView.findViewById(R.id.btnDelete);
            convertView.setTag(holder);
        } else {
            holder = (ViewHolder) convertView.getTag();
        }

        HistoryItem item = historyList.get(position);
        
        holder.checkBox.setChecked(item.isSelected());
        holder.tvTitle.setText(item.getTitle() != null && !item.getTitle().isEmpty() ? item.getTitle() : "无标题");
        holder.tvUrl.setText(item.getUrl());
        holder.tvTime.setText(item.getFormattedTime());

        // 设置点击事件
        convertView.setOnClickListener(v -> {
            if (listener != null) {
                listener.onItemClick(item);
            }
        });

        holder.checkBox.setOnCheckedChangeListener((buttonView, isChecked) -> {
            item.setSelected(isChecked);
            if (listener != null) {
                listener.onCheckChanged(item, isChecked);
            }
        });

        holder.btnDelete.setOnClickListener(v -> {
            if (listener != null) {
                listener.onDeleteClick(item);
            }
        });

        return convertView;
    }

    public void updateData(List<HistoryItem> newHistoryList) {
        this.historyList = newHistoryList;
        notifyDataSetChanged();
    }

    private static class ViewHolder {
        CheckBox checkBox;
        TextView tvTitle;
        TextView tvUrl;
        TextView tvTime;
        Button btnDelete;
    }
}