package com.example.study_android;

import android.app.AlertDialog;
import android.content.Intent;
import android.os.Bundle;
import android.webkit.WebView;
import android.widget.Button;
import android.widget.Switch;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;

public class SettingsActivity extends AppCompatActivity {
    private Button btnBack, btnClearCache, btnAbout;
    private Switch switchCache, switchOffline, switchFullscreen;
    private SettingsManager settingsManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_settings);

        initViews();
        initData();
        setupListeners();
    }

    private void initViews() {
        btnBack = findViewById(R.id.btnBack);
        btnClearCache = findViewById(R.id.btnClearCache);
        btnAbout = findViewById(R.id.btnAbout);
        switchCache = findViewById(R.id.switchCache);
        switchOffline = findViewById(R.id.switchOffline);
        switchFullscreen = findViewById(R.id.switchFullscreen);
    }

    private void initData() {
        settingsManager = new SettingsManager(this);
        
        // 加载当前设置
        switchCache.setChecked(settingsManager.isCacheEnabled());
        switchOffline.setChecked(settingsManager.isOfflineMode());
        switchFullscreen.setChecked(settingsManager.isFullscreenMode());
    }

    private void setupListeners() {
        btnBack.setOnClickListener(v -> {
            // 返回设置结果到主界面
            Intent intent = new Intent();
            intent.putExtra("settings_changed", true);
            setResult(RESULT_OK, intent);
            finish();
        });

        switchCache.setOnCheckedChangeListener((buttonView, isChecked) -> {
            settingsManager.setCacheEnabled(isChecked);
            Toast.makeText(this, isChecked ? "缓存已启用" : "缓存已禁用", Toast.LENGTH_SHORT).show();
        });

        switchOffline.setOnCheckedChangeListener((buttonView, isChecked) -> {
            settingsManager.setOfflineMode(isChecked);
            Toast.makeText(this, isChecked ? "离线模式已启用" : "离线模式已禁用", Toast.LENGTH_SHORT).show();
        });

        switchFullscreen.setOnCheckedChangeListener((buttonView, isChecked) -> {
            settingsManager.setFullscreenMode(isChecked);
            Toast.makeText(this, isChecked ? "全屏模式已启用" : "全屏模式已禁用", Toast.LENGTH_SHORT).show();
        });

        btnClearCache.setOnClickListener(v -> {
            new AlertDialog.Builder(this)
                    .setTitle("确认清理")
                    .setMessage("确定要清理WebView缓存吗？")
                    .setPositiveButton("确定", (dialog, which) -> {
                        clearWebViewCache();
                        Toast.makeText(this, "缓存清理完成", Toast.LENGTH_SHORT).show();
                    })
                    .setNegativeButton("取消", null)
                    .show();
        });

        btnAbout.setOnClickListener(v -> showAboutDialog());
    }

    private void clearWebViewCache() {
        // 清理WebView缓存
        WebView webView = new WebView(this);
        webView.clearCache(true);
        webView.clearHistory();
        webView.clearFormData();
        
        // 清理应用缓存目录
        try {
            deleteDir(getCacheDir());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private boolean deleteDir(java.io.File dir) {
        if (dir != null && dir.isDirectory()) {
            String[] children = dir.list();
            if (children != null) {
                for (String child : children) {
                    boolean success = deleteDir(new java.io.File(dir, child));
                    if (!success) {
                        return false;
                    }
                }
            }
            return dir.delete();
        } else if (dir != null && dir.isFile()) {
            return dir.delete();
        } else {
            return false;
        }
    }

    private void showAboutDialog() {
        new AlertDialog.Builder(this)
                .setTitle("关于应用")
                .setMessage("网页加载器 v1.0\n\n功能特性：\n• 网页浏览和加载\n• 浏览历史管理\n• 缓存控制\n• 离线模式\n• 全屏显示\n• 历史记录导入导出\n\n开发者：Android学习项目")
                .setPositiveButton("确定", null)
                .show();
    }

    @Override
    public void onBackPressed() {
        // 返回设置结果到主界面
        Intent intent = new Intent();
        intent.putExtra("settings_changed", true);
        setResult(RESULT_OK, intent);
        super.onBackPressed();
    }
}